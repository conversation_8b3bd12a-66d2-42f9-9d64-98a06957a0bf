import {
  EnhancedTodoItem,
  TodoColumn,
  EnhancedTodoBoard,
  Subject,
  PresetExamType,
  AnalyticsData,
  FilterState,
  SearchState,
} from '@/types/todo';
import { v4 as uuidv4 } from 'uuid';

// Storage keys
const STORAGE_KEYS = {
  TASKS: 'isotope_tasks',
  COLUMNS: 'isotope_task_columns',
  SUBJECTS: 'isotope_user_subjects',
  ANALYTICS: 'isotope_task_analytics',
} as const;

// Default columns for new users
const DEFAULT_COLUMNS: TodoColumn[] = [
  {
    id: 'column-1',
    title: 'To Do',
    color: '#6366f1',
    taskIds: [],
    isDefault: true,
  },
  {
    id: 'column-2',
    title: 'In Progress',
    color: '#f59e0b',
    taskIds: [],
    isDefault: true,
  },
  {
    id: 'column-3',
    title: 'Done',
    color: '#10b981',
    taskIds: [],
    isDefault: true,
  },
];

// Helper functions for safe localStorage operations
const safeParseJSON = <T>(data: string | null, defaultValue: T): T => {
  try {
    return data ? JSON.parse(data) : defaultValue;
  } catch (error) {
    console.error('Error parsing JSON from localStorage:', error);
    return defaultValue;
  }
};

const safeSaveToStorage = (key: string, data: any): void => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving to localStorage:', error);
    throw new Error('Failed to save data to local storage');
  }
};

// Get user-specific storage keys
const getUserStorageKey = (userId: string, type: keyof typeof STORAGE_KEYS): string => {
  return `${STORAGE_KEYS[type]}_${userId}`;
};

// Core task operations
export const taskLocalStorage = {
  // Get all tasks for a user
  getAllTasks: (userId: string): EnhancedTodoItem[] => {
    try {
      const storageKey = getUserStorageKey(userId, 'TASKS');
      const data = localStorage.getItem(storageKey);
      const tasks = safeParseJSON<EnhancedTodoItem[]>(data, []);
      
      // Calculate computed fields for each task
      return tasks.map(task => calculateComputedFields(task, tasks));
    } catch (error) {
      console.error('Error loading tasks:', error);
      return [];
    }
  },

  // Save a task (create or update)
  saveTask: (userId: string, task: EnhancedTodoItem): EnhancedTodoItem => {
    try {
      const storageKey = getUserStorageKey(userId, 'TASKS');
      const existingTasks = taskLocalStorage.getAllTasks(userId);
      
      // Ensure task has required fields
      const taskToSave: EnhancedTodoItem = {
        ...task,
        id: task.id || uuidv4(),
        createdAt: task.createdAt || Date.now(),
        updatedAt: Date.now(),
        tags: task.tags || [],
        chapterTags: task.chapterTags || [],
        completionPercentage: task.completionPercentage || 0,
        viewCount: task.viewCount || 0,
        difficultyLevel: task.difficultyLevel || 'medium',
      };

      // Update or add task
      const existingIndex = existingTasks.findIndex(t => t.id === taskToSave.id);
      let updatedTasks: EnhancedTodoItem[];
      
      if (existingIndex >= 0) {
        updatedTasks = [...existingTasks];
        updatedTasks[existingIndex] = taskToSave;
      } else {
        updatedTasks = [...existingTasks, taskToSave];
        
        // Add task to default column if no columnId specified
        if (!taskToSave.columnId) {
          taskToSave.columnId = 'column-1';
          taskLocalStorage.addTaskToColumn(userId, 'column-1', taskToSave.id);
        }
      }

      safeSaveToStorage(storageKey, updatedTasks);
      return calculateComputedFields(taskToSave, updatedTasks);
    } catch (error) {
      console.error('Error saving task:', error);
      throw new Error('Failed to save task');
    }
  },

  // Delete a task
  deleteTask: (userId: string, taskId: string): void => {
    try {
      const storageKey = getUserStorageKey(userId, 'TASKS');
      const existingTasks = taskLocalStorage.getAllTasks(userId);
      const updatedTasks = existingTasks.filter(task => task.id !== taskId);
      
      // Remove from columns
      const columns = taskLocalStorage.getAllColumns(userId);
      columns.forEach(column => {
        if (column.taskIds.includes(taskId)) {
          taskLocalStorage.removeTaskFromColumn(userId, column.id, taskId);
        }
      });
      
      safeSaveToStorage(storageKey, updatedTasks);
    } catch (error) {
      console.error('Error deleting task:', error);
      throw new Error('Failed to delete task');
    }
  },

  // Get tasks with hierarchy (parent-child relationships)
  getTasksWithHierarchy: (userId: string): EnhancedTodoItem[] => {
    const allTasks = taskLocalStorage.getAllTasks(userId);
    return allTasks.map(task => ({
      ...task,
      depth: calculateTaskDepth(task, allTasks),
      subtasks: allTasks.filter(t => t.parentId === task.id).map(t => t.id),
      hasSubtasks: allTasks.some(t => t.parentId === task.id),
    }));
  },

  // Bulk update tasks
  bulkUpdateTasks: (userId: string, updates: Array<{ id: string; updates: Partial<EnhancedTodoItem> }>): EnhancedTodoItem[] => {
    try {
      const storageKey = getUserStorageKey(userId, 'TASKS');
      const existingTasks = taskLocalStorage.getAllTasks(userId);
      
      const updatedTasks = existingTasks.map(task => {
        const update = updates.find(u => u.id === task.id);
        if (update) {
          return { ...task, ...update.updates, updatedAt: Date.now() };
        }
        return task;
      });
      
      safeSaveToStorage(storageKey, updatedTasks);
      return updatedTasks.map(task => calculateComputedFields(task, updatedTasks));
    } catch (error) {
      console.error('Error bulk updating tasks:', error);
      throw new Error('Failed to bulk update tasks');
    }
  },

  // Search tasks
  searchTasks: (userId: string, query: string): EnhancedTodoItem[] => {
    const allTasks = taskLocalStorage.getAllTasks(userId);
    const lowercaseQuery = query.toLowerCase();
    
    return allTasks.filter(task => {
      return (
        task.title.toLowerCase().includes(lowercaseQuery) ||
        task.description.toLowerCase().includes(lowercaseQuery) ||
        task.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
        task.chapterTags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
        (task.notes && task.notes.toLowerCase().includes(lowercaseQuery))
      );
    });
  },

  // Filter tasks
  filterTasks: (userId: string, filters: FilterState): EnhancedTodoItem[] => {
    const allTasks = taskLocalStorage.getAllTasks(userId);
    
    return allTasks.filter(task => {
      // Priority filter
      if (filters.priority.length > 0 && !filters.priority.includes(task.priority)) {
        return false;
      }
      
      // Subject filter
      if (filters.subjects.length > 0 && (!task.subjectId || !filters.subjects.includes(task.subjectId))) {
        return false;
      }
      
      // Exam filter
      if (filters.exams.length > 0 && (!task.examId || !filters.exams.includes(task.examId))) {
        return false;
      }
      
      // Difficulty filter
      if (filters.difficulty.length > 0 && !filters.difficulty.includes(task.difficultyLevel)) {
        return false;
      }
      
      // Tags filter
      if (filters.tags.length > 0 && !filters.tags.some(tag => task.tags.includes(tag))) {
        return false;
      }
      
      // Date range filter
      if (filters.dateRange.start && task.createdAt < filters.dateRange.start) {
        return false;
      }
      if (filters.dateRange.end && task.createdAt > filters.dateRange.end) {
        return false;
      }
      
      return true;
    });
  },
};

// Column operations
export const columnLocalStorage = {
  // Get all columns for a user
  getAllColumns: (userId: string): TodoColumn[] => {
    try {
      const storageKey = getUserStorageKey(userId, 'COLUMNS');
      const data = localStorage.getItem(storageKey);
      const columns = safeParseJSON<TodoColumn[]>(data, DEFAULT_COLUMNS);
      
      // Ensure default columns exist
      if (columns.length === 0) {
        safeSaveToStorage(storageKey, DEFAULT_COLUMNS);
        return DEFAULT_COLUMNS;
      }
      
      return columns;
    } catch (error) {
      console.error('Error loading columns:', error);
      return DEFAULT_COLUMNS;
    }
  },

  // Save a column (create or update)
  saveColumn: (userId: string, column: TodoColumn): TodoColumn => {
    try {
      const storageKey = getUserStorageKey(userId, 'COLUMNS');
      const existingColumns = columnLocalStorage.getAllColumns(userId);
      
      const columnToSave: TodoColumn = {
        ...column,
        id: column.id || uuidv4(),
        taskIds: column.taskIds || [],
      };

      const existingIndex = existingColumns.findIndex(c => c.id === columnToSave.id);
      let updatedColumns: TodoColumn[];
      
      if (existingIndex >= 0) {
        updatedColumns = [...existingColumns];
        updatedColumns[existingIndex] = columnToSave;
      } else {
        updatedColumns = [...existingColumns, columnToSave];
      }

      safeSaveToStorage(storageKey, updatedColumns);
      return columnToSave;
    } catch (error) {
      console.error('Error saving column:', error);
      throw new Error('Failed to save column');
    }
  },

  // Delete a column
  deleteColumn: (userId: string, columnId: string): void => {
    try {
      const storageKey = getUserStorageKey(userId, 'COLUMNS');
      const existingColumns = columnLocalStorage.getAllColumns(userId);
      
      // Don't allow deleting default columns
      const columnToDelete = existingColumns.find(c => c.id === columnId);
      if (columnToDelete?.isDefault) {
        throw new Error('Cannot delete default columns');
      }
      
      const updatedColumns = existingColumns.filter(column => column.id !== columnId);
      safeSaveToStorage(storageKey, updatedColumns);
    } catch (error) {
      console.error('Error deleting column:', error);
      throw new Error('Failed to delete column');
    }
  },

  // Add task to column
  addTaskToColumn: (userId: string, columnId: string, taskId: string): void => {
    try {
      const storageKey = getUserStorageKey(userId, 'COLUMNS');
      const columns = columnLocalStorage.getAllColumns(userId);
      
      const updatedColumns = columns.map(column => {
        if (column.id === columnId && !column.taskIds.includes(taskId)) {
          return { ...column, taskIds: [...column.taskIds, taskId] };
        }
        return column;
      });
      
      safeSaveToStorage(storageKey, updatedColumns);
    } catch (error) {
      console.error('Error adding task to column:', error);
      throw new Error('Failed to add task to column');
    }
  },

  // Remove task from column
  removeTaskFromColumn: (userId: string, columnId: string, taskId: string): void => {
    try {
      const storageKey = getUserStorageKey(userId, 'COLUMNS');
      const columns = columnLocalStorage.getAllColumns(userId);
      
      const updatedColumns = columns.map(column => {
        if (column.id === columnId) {
          return { ...column, taskIds: column.taskIds.filter(id => id !== taskId) };
        }
        return column;
      });
      
      safeSaveToStorage(storageKey, updatedColumns);
    } catch (error) {
      console.error('Error removing task from column:', error);
      throw new Error('Failed to remove task from column');
    }
  },

  // Move task between columns
  moveTaskBetweenColumns: (userId: string, taskId: string, sourceColumnId: string, destinationColumnId: string, destinationIndex: number): void => {
    try {
      const storageKey = getUserStorageKey(userId, 'COLUMNS');
      const columns = columnLocalStorage.getAllColumns(userId);
      
      const updatedColumns = columns.map(column => {
        if (column.id === sourceColumnId) {
          // Remove from source
          return { ...column, taskIds: column.taskIds.filter(id => id !== taskId) };
        } else if (column.id === destinationColumnId) {
          // Add to destination at specific index
          const newTaskIds = [...column.taskIds];
          newTaskIds.splice(destinationIndex, 0, taskId);
          return { ...column, taskIds: newTaskIds };
        }
        return column;
      });
      
      safeSaveToStorage(storageKey, updatedColumns);
      
      // Update task's columnId
      const tasks = taskLocalStorage.getAllTasks(userId);
      const taskToUpdate = tasks.find(t => t.id === taskId);
      if (taskToUpdate) {
        taskLocalStorage.saveTask(userId, { ...taskToUpdate, columnId: destinationColumnId });
      }
    } catch (error) {
      console.error('Error moving task between columns:', error);
      throw new Error('Failed to move task between columns');
    }
  },
};

// Helper functions
const calculateComputedFields = (task: EnhancedTodoItem, allTasks: EnhancedTodoItem[]): EnhancedTodoItem => {
  return {
    ...task,
    depth: calculateTaskDepth(task, allTasks),
    subtasks: allTasks.filter(t => t.parentId === task.id).map(t => t.id),
    hasSubtasks: allTasks.some(t => t.parentId === task.id),
    isOverdue: task.dueDate ? task.dueDate < Date.now() : false,
  };
};

const calculateTaskDepth = (task: EnhancedTodoItem, allTasks: EnhancedTodoItem[]): number => {
  if (!task.parentId) return 0;
  
  const parent = allTasks.find(t => t.id === task.parentId);
  if (!parent) return 0;
  
  return 1 + calculateTaskDepth(parent, allTasks);
};

// Analytics operations
export const analyticsLocalStorage = {
  // Calculate task analytics
  calculateAnalytics: (userId: string): AnalyticsData => {
    const tasks = taskLocalStorage.getAllTasks(userId);
    const now = Date.now();
    const today = new Date().setHours(0, 0, 0, 0);
    const thisWeek = now - (7 * 24 * 60 * 60 * 1000);
    const thisMonth = now - (30 * 24 * 60 * 60 * 1000);

    // Basic stats
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(t => t.completionPercentage === 100).length;
    const inProgressTasks = tasks.filter(t => t.completionPercentage > 0 && t.completionPercentage < 100).length;
    const overdueTasks = tasks.filter(t => t.dueDate && t.dueDate < now && t.completionPercentage < 100).length;

    // Time-based stats
    const tasksCreatedToday = tasks.filter(t => t.createdAt >= today).length;
    const tasksCompletedToday = tasks.filter(t => t.completionPercentage === 100 && t.updatedAt >= today).length;
    const tasksCreatedThisWeek = tasks.filter(t => t.createdAt >= thisWeek).length;
    const tasksCompletedThisWeek = tasks.filter(t => t.completionPercentage === 100 && t.updatedAt >= thisWeek).length;

    // Priority distribution
    const priorityStats = {
      high: tasks.filter(t => t.priority === 'high').length,
      medium: tasks.filter(t => t.priority === 'medium').length,
      low: tasks.filter(t => t.priority === 'low').length,
    };

    // Subject distribution
    const subjectStats = tasks.reduce((acc, task) => {
      if (task.subjectId) {
        acc[task.subjectId] = (acc[task.subjectId] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    // Difficulty distribution
    const difficultyStats = {
      easy: tasks.filter(t => t.difficultyLevel === 'easy').length,
      medium: tasks.filter(t => t.difficultyLevel === 'medium').length,
      hard: tasks.filter(t => t.difficultyLevel === 'hard').length,
    };

    // Productivity metrics
    const averageCompletionTime = calculateAverageCompletionTime(tasks);
    const productivityScore = calculateProductivityScore(tasks);
    const streakData = calculateStreakData(tasks);

    return {
      totalTasks,
      completedTasks,
      inProgressTasks,
      overdueTasks,
      completionRate: totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0,
      tasksCreatedToday,
      tasksCompletedToday,
      tasksCreatedThisWeek,
      tasksCompletedThisWeek,
      priorityStats,
      subjectStats,
      difficultyStats,
      averageCompletionTime,
      productivityScore,
      streakData,
      lastUpdated: now,
    };
  },

  // Save analytics data
  saveAnalytics: (userId: string, analytics: AnalyticsData): void => {
    try {
      const storageKey = getUserStorageKey(userId, 'ANALYTICS');
      safeSaveToStorage(storageKey, analytics);
    } catch (error) {
      console.error('Error saving analytics:', error);
    }
  },

  // Get cached analytics
  getCachedAnalytics: (userId: string): AnalyticsData | null => {
    try {
      const storageKey = getUserStorageKey(userId, 'ANALYTICS');
      const data = localStorage.getItem(storageKey);
      return safeParseJSON<AnalyticsData | null>(data, null);
    } catch (error) {
      console.error('Error loading cached analytics:', error);
      return null;
    }
  },
};

// Import/Export operations
export const importExportLocalStorage = {
  // Export tasks to JSON
  exportTasks: (userId: string): string => {
    const tasks = taskLocalStorage.getAllTasks(userId);
    const columns = columnLocalStorage.getAllColumns(userId);
    const analytics = analyticsLocalStorage.getCachedAnalytics(userId);

    const exportData = {
      tasks,
      columns,
      analytics,
      exportedAt: Date.now(),
      version: '1.0',
    };

    return JSON.stringify(exportData, null, 2);
  },

  // Import tasks from JSON
  importTasks: (userId: string, jsonData: string): { success: boolean; imported: number; errors: string[] } => {
    try {
      const importData = JSON.parse(jsonData);
      const errors: string[] = [];
      let imported = 0;

      // Validate import data
      if (!importData.tasks || !Array.isArray(importData.tasks)) {
        throw new Error('Invalid import data: tasks array not found');
      }

      // Import tasks
      for (const taskData of importData.tasks) {
        try {
          // Generate new ID to avoid conflicts
          const newTask: EnhancedTodoItem = {
            ...taskData,
            id: uuidv4(),
            createdAt: Date.now(),
            updatedAt: Date.now(),
            createdBy: userId,
          };

          taskLocalStorage.saveTask(userId, newTask);
          imported++;
        } catch (error) {
          errors.push(`Failed to import task "${taskData.title}": ${error}`);
        }
      }

      // Import columns if available
      if (importData.columns && Array.isArray(importData.columns)) {
        for (const columnData of importData.columns) {
          try {
            if (!columnData.isDefault) { // Don't import default columns
              const newColumn: TodoColumn = {
                ...columnData,
                id: uuidv4(),
                taskIds: [], // Reset task IDs
              };
              columnLocalStorage.saveColumn(userId, newColumn);
            }
          } catch (error) {
            errors.push(`Failed to import column "${columnData.title}": ${error}`);
          }
        }
      }

      return { success: true, imported, errors };
    } catch (error) {
      return { success: false, imported: 0, errors: [error.message] };
    }
  },

  // Export to CSV
  exportToCSV: (userId: string): string => {
    const tasks = taskLocalStorage.getAllTasks(userId);

    const headers = [
      'Title',
      'Description',
      'Priority',
      'Status',
      'Difficulty',
      'Subject',
      'Tags',
      'Due Date',
      'Completion %',
      'Created At',
      'Updated At'
    ];

    const rows = tasks.map(task => [
      task.title,
      task.description,
      task.priority,
      task.completionPercentage === 100 ? 'Completed' : task.completionPercentage > 0 ? 'In Progress' : 'To Do',
      task.difficultyLevel,
      task.subjectId || '',
      task.tags.join('; '),
      task.dueDate ? new Date(task.dueDate).toLocaleDateString() : '',
      task.completionPercentage,
      new Date(task.createdAt).toLocaleDateString(),
      new Date(task.updatedAt).toLocaleDateString()
    ]);

    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    return csvContent;
  },
};

// Utility functions for analytics
const calculateAverageCompletionTime = (tasks: EnhancedTodoItem[]): number => {
  const completedTasks = tasks.filter(t => t.completionPercentage === 100);
  if (completedTasks.length === 0) return 0;

  const totalTime = completedTasks.reduce((sum, task) => {
    return sum + (task.updatedAt - task.createdAt);
  }, 0);

  return totalTime / completedTasks.length;
};

const calculateProductivityScore = (tasks: EnhancedTodoItem[]): number => {
  if (tasks.length === 0) return 0;

  const completedTasks = tasks.filter(t => t.completionPercentage === 100).length;
  const overdueTasks = tasks.filter(t => t.dueDate && t.dueDate < Date.now() && t.completionPercentage < 100).length;
  const highPriorityCompleted = tasks.filter(t => t.priority === 'high' && t.completionPercentage === 100).length;
  const totalHighPriority = tasks.filter(t => t.priority === 'high').length;

  // Calculate score based on completion rate, overdue penalty, and high priority completion
  const completionScore = (completedTasks / tasks.length) * 60;
  const overdueScore = Math.max(0, 20 - (overdueTasks * 5));
  const priorityScore = totalHighPriority > 0 ? (highPriorityCompleted / totalHighPriority) * 20 : 20;

  return Math.min(100, completionScore + overdueScore + priorityScore);
};

const calculateStreakData = (tasks: EnhancedTodoItem[]): { current: number; longest: number; lastActivity: number } => {
  const completedTasks = tasks
    .filter(t => t.completionPercentage === 100)
    .sort((a, b) => a.updatedAt - b.updatedAt);

  if (completedTasks.length === 0) {
    return { current: 0, longest: 0, lastActivity: 0 };
  }

  const dayInMs = 24 * 60 * 60 * 1000;
  const today = new Date().setHours(0, 0, 0, 0);

  // Group tasks by day
  const tasksByDay = completedTasks.reduce((acc, task) => {
    const day = new Date(task.updatedAt).setHours(0, 0, 0, 0);
    acc[day] = (acc[day] || 0) + 1;
    return acc;
  }, {} as Record<number, number>);

  const days = Object.keys(tasksByDay).map(Number).sort((a, b) => b - a);

  // Calculate current streak
  let currentStreak = 0;
  let checkDay = today;

  while (days.includes(checkDay)) {
    currentStreak++;
    checkDay -= dayInMs;
  }

  // Calculate longest streak
  let longestStreak = 0;
  let tempStreak = 0;

  for (let i = days.length - 1; i >= 0; i--) {
    if (i === days.length - 1 || days[i] === days[i + 1] + dayInMs) {
      tempStreak++;
      longestStreak = Math.max(longestStreak, tempStreak);
    } else {
      tempStreak = 1;
    }
  }

  return {
    current: currentStreak,
    longest: longestStreak,
    lastActivity: completedTasks[completedTasks.length - 1]?.updatedAt || 0,
  };
};

// Board operations
export const boardLocalStorage = {
  // Get complete board data
  getBoard: (userId: string): EnhancedTodoBoard => {
    const columns = columnLocalStorage.getAllColumns(userId);
    const allTasks = taskLocalStorage.getAllTasks(userId);

    // Organize tasks by columns
    const columnsWithTasks = columns.map(column => ({
      ...column,
      tasks: column.taskIds
        .map(taskId => allTasks.find(task => task.id === taskId))
        .filter(Boolean) as EnhancedTodoItem[],
    }));

    return {
      id: `board_${userId}`,
      columns: columnsWithTasks,
      tasks: allTasks,
    };
  },

  // Update board structure
  updateBoard: (userId: string, board: EnhancedTodoBoard): void => {
    // Save columns
    board.columns.forEach(column => {
      columnLocalStorage.saveColumn(userId, {
        id: column.id,
        title: column.title,
        color: column.color,
        taskIds: column.tasks.map(task => task.id),
        isDefault: column.isDefault,
      });
    });

    // Save tasks
    board.tasks.forEach(task => {
      taskLocalStorage.saveTask(userId, task);
    });
  },
};

// Migration utilities
export const migrationLocalStorage = {
  // Check if migration is needed
  isMigrationNeeded: (userId: string): boolean => {
    const tasksKey = getUserStorageKey(userId, 'TASKS');
    return !localStorage.getItem(tasksKey);
  },

  // Migrate from old storage format
  migrateFromLegacy: (userId: string): void => {
    try {
      // Check for legacy task storage formats
      const legacyKeys = [
        `todos_${userId}`,
        `tasks_${userId}`,
        `enhanced_todos_${userId}`,
      ];

      for (const legacyKey of legacyKeys) {
        const legacyData = localStorage.getItem(legacyKey);
        if (legacyData) {
          const legacyTasks = safeParseJSON<any[]>(legacyData, []);

          // Convert legacy tasks to new format
          legacyTasks.forEach(legacyTask => {
            const newTask: EnhancedTodoItem = {
              id: legacyTask.id || uuidv4(),
              title: legacyTask.title || 'Untitled Task',
              description: legacyTask.description || '',
              priority: legacyTask.priority || 'medium',
              createdAt: legacyTask.createdAt || Date.now(),
              updatedAt: legacyTask.updatedAt || Date.now(),
              createdBy: userId,
              columnId: legacyTask.columnId || 'column-1',
              tags: legacyTask.tags || [],
              chapterTags: legacyTask.chapterTags || [],
              difficultyLevel: legacyTask.difficultyLevel || 'medium',
              completionPercentage: legacyTask.completionPercentage || 0,
              viewCount: legacyTask.viewCount || 0,
              dueDate: legacyTask.dueDate,
              assignedTo: legacyTask.assignedTo,
              assignedToName: legacyTask.assignedToName,
              assignedToPhotoURL: legacyTask.assignedToPhotoURL,
              parentId: legacyTask.parentId,
              subjectId: legacyTask.subjectId,
              examId: legacyTask.examId,
              timeEstimate: legacyTask.timeEstimate,
              actualTimeSpent: legacyTask.actualTimeSpent,
              notes: legacyTask.notes,
              lastViewed: legacyTask.lastViewed,
            };

            taskLocalStorage.saveTask(userId, newTask);
          });

          // Remove legacy data
          localStorage.removeItem(legacyKey);
          console.log(`Migrated ${legacyTasks.length} tasks from ${legacyKey}`);
        }
      }
    } catch (error) {
      console.error('Error during migration:', error);
    }
  },

  // Clear all task data for a user
  clearAllData: (userId: string): void => {
    const keys = Object.values(STORAGE_KEYS).map(key => getUserStorageKey(userId, key as keyof typeof STORAGE_KEYS));
    keys.forEach(key => localStorage.removeItem(key));
  },
};

// Add missing functions to taskLocalStorage
taskLocalStorage.getAllColumns = columnLocalStorage.getAllColumns;
taskLocalStorage.addTaskToColumn = columnLocalStorage.addTaskToColumn;
taskLocalStorage.removeTaskFromColumn = columnLocalStorage.removeTaskFromColumn;

// Export main storage object
export const taskStorage = {
  ...taskLocalStorage,
  ...columnLocalStorage,
  ...analyticsLocalStorage,
  ...importExportLocalStorage,
  ...boardLocalStorage,
  ...migrationLocalStorage,
};
