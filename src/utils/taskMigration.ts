import { taskStorage } from './taskLocalStorage';
import { EnhancedTodoItem } from '@/types/todo';
import { supabase } from '@/integrations/supabase/client';

/**
 * Task Migration Utility
 * Handles migration from Supabase to local storage for tasks
 */

interface MigrationResult {
  success: boolean;
  tasksImported: number;
  columnsImported: number;
  errors: string[];
}

interface SupabaseTodoRow {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  created_at: string;
  updated_at: string;
  created_by: string;
  due_date?: string;
  assigned_to?: string;
  assigned_to_name?: string;
  assigned_to_photo_url?: string;
  column_id?: string;
  group_id?: string;
  // Enhanced fields (may not exist in all records)
  parent_id?: string;
  subject_id?: string;
  exam_id?: string;
  tags?: string[];
  chapter_tags?: string[];
  difficulty_level?: 'easy' | 'medium' | 'hard';
  time_estimate?: number;
  actual_time_spent?: number;
  completion_percentage?: number;
  notes?: string;
  view_count?: number;
  last_viewed?: string;
}

export const taskMigration = {
  /**
   * Check if user has data in Supabase that needs migration
   */
  async checkMigrationNeeded(userId: string): Promise<boolean> {
    try {
      // Check if local storage already has data
      const localTasks = taskStorage.getAllTasks(userId);
      if (localTasks.length > 0) {
        return false; // Already has local data, no migration needed
      }

      // Check if Supabase has data
      const { data, error } = await supabase
        .from('todos')
        .select('id')
        .eq('created_by', userId)
        .limit(1);

      if (error) {
        console.error('Error checking Supabase data:', error);
        return false;
      }

      return (data?.length || 0) > 0;
    } catch (error) {
      console.error('Error checking migration status:', error);
      return false;
    }
  },

  /**
   * Migrate all tasks from Supabase to local storage
   */
  async migrateFromSupabase(userId: string): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      tasksImported: 0,
      columnsImported: 0,
      errors: [],
    };

    try {
      console.log('Starting task migration for user:', userId);

      // Fetch all tasks from Supabase
      const { data: supabaseTasks, error: tasksError } = await supabase
        .from('todos')
        .select('*')
        .eq('created_by', userId)
        .order('created_at', { ascending: false });

      if (tasksError) {
        result.errors.push(`Failed to fetch tasks from Supabase: ${tasksError.message}`);
        return result;
      }

      if (!supabaseTasks || supabaseTasks.length === 0) {
        console.log('No tasks found in Supabase for user:', userId);
        result.success = true;
        return result;
      }

      console.log(`Found ${supabaseTasks.length} tasks in Supabase`);

      // Convert and save each task
      for (const supabaseTask of supabaseTasks) {
        try {
          const localTask = convertSupabaseToLocal(supabaseTask as any, userId);
          taskStorage.saveTask(userId, localTask);
          result.tasksImported++;
        } catch (error) {
          const errorMsg = `Failed to migrate task ${supabaseTask.id}: ${error}`;
          console.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      // Ensure default columns exist
      const columns = taskStorage.getAllColumns(userId);
      result.columnsImported = columns.length;

      result.success = result.errors.length === 0 || result.tasksImported > 0;
      
      console.log(`Migration completed: ${result.tasksImported} tasks imported, ${result.errors.length} errors`);
      
      return result;
    } catch (error) {
      const errorMsg = `Migration failed: ${error}`;
      console.error(errorMsg);
      result.errors.push(errorMsg);
      return result;
    }
  },

  /**
   * Backup local storage data to JSON
   */
  async backupLocalData(userId: string): Promise<string> {
    try {
      const tasks = taskStorage.getAllTasks(userId);
      const columns = taskStorage.getAllColumns(userId);
      const analytics = taskStorage.getCachedAnalytics(userId);

      const backup = {
        version: '1.0',
        timestamp: Date.now(),
        userId,
        tasks,
        columns,
        analytics,
      };

      return JSON.stringify(backup, null, 2);
    } catch (error) {
      console.error('Error creating backup:', error);
      throw new Error('Failed to create backup');
    }
  },

  /**
   * Restore data from backup
   */
  async restoreFromBackup(userId: string, backupData: string): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: false,
      tasksImported: 0,
      columnsImported: 0,
      errors: [],
    };

    try {
      const backup = JSON.parse(backupData);

      if (!backup.tasks || !Array.isArray(backup.tasks)) {
        result.errors.push('Invalid backup format: tasks array not found');
        return result;
      }

      // Clear existing data
      taskStorage.clearAllData(userId);

      // Restore tasks
      for (const task of backup.tasks) {
        try {
          taskStorage.saveTask(userId, task);
          result.tasksImported++;
        } catch (error) {
          result.errors.push(`Failed to restore task ${task.id}: ${error}`);
        }
      }

      // Restore columns if available
      if (backup.columns && Array.isArray(backup.columns)) {
        for (const column of backup.columns) {
          try {
            taskStorage.saveColumn(userId, column);
            result.columnsImported++;
          } catch (error) {
            result.errors.push(`Failed to restore column ${column.id}: ${error}`);
          }
        }
      }

      // Restore analytics if available
      if (backup.analytics) {
        try {
          taskStorage.saveAnalytics(userId, backup.analytics);
        } catch (error) {
          result.errors.push(`Failed to restore analytics: ${error}`);
        }
      }

      result.success = result.tasksImported > 0;
      return result;
    } catch (error) {
      result.errors.push(`Failed to parse backup data: ${error}`);
      return result;
    }
  },

  /**
   * Clear all local data for a user
   */
  async clearLocalData(userId: string): Promise<void> {
    try {
      taskStorage.clearAllData(userId);
      console.log('Local data cleared for user:', userId);
    } catch (error) {
      console.error('Error clearing local data:', error);
      throw new Error('Failed to clear local data');
    }
  },

  /**
   * Get migration status and statistics
   */
  async getMigrationStatus(userId: string): Promise<{
    hasLocalData: boolean;
    hasSupabaseData: boolean;
    localTaskCount: number;
    supabaseTaskCount: number;
    migrationNeeded: boolean;
  }> {
    try {
      // Check local data
      const localTasks = taskStorage.getAllTasks(userId);
      const hasLocalData = localTasks.length > 0;

      // Check Supabase data
      let hasSupabaseData = false;
      let supabaseTaskCount = 0;

      try {
        const { data, error } = await supabase
          .from('todos')
          .select('id')
          .eq('created_by', userId);

        if (!error && data) {
          supabaseTaskCount = data.length;
          hasSupabaseData = supabaseTaskCount > 0;
        }
      } catch (error) {
        console.warn('Could not check Supabase data:', error);
      }

      return {
        hasLocalData,
        hasSupabaseData,
        localTaskCount: localTasks.length,
        supabaseTaskCount,
        migrationNeeded: hasSupabaseData && !hasLocalData,
      };
    } catch (error) {
      console.error('Error getting migration status:', error);
      return {
        hasLocalData: false,
        hasSupabaseData: false,
        localTaskCount: 0,
        supabaseTaskCount: 0,
        migrationNeeded: false,
      };
    }
  },
};

/**
 * Convert Supabase task row to local storage format
 */
function convertSupabaseToLocal(supabaseTask: any, userId: string): EnhancedTodoItem {
  // Safely convert priority with fallback
  const priority = ['low', 'medium', 'high'].includes(supabaseTask.priority)
    ? supabaseTask.priority as 'low' | 'medium' | 'high'
    : 'medium';

  // Safely convert difficulty level with fallback
  const difficultyLevel = ['easy', 'medium', 'hard'].includes(supabaseTask.difficulty_level)
    ? supabaseTask.difficulty_level as 'easy' | 'medium' | 'hard'
    : 'medium';

  return {
    id: supabaseTask.id,
    title: supabaseTask.title || 'Untitled Task',
    description: supabaseTask.description || '',
    priority,
    createdAt: supabaseTask.created_at ? new Date(supabaseTask.created_at).getTime() : Date.now(),
    updatedAt: supabaseTask.updated_at ? new Date(supabaseTask.updated_at).getTime() : Date.now(),
    createdBy: userId,
    dueDate: supabaseTask.due_date ? new Date(supabaseTask.due_date).getTime() : undefined,
    assignedTo: supabaseTask.assigned_to,
    assignedToName: supabaseTask.assigned_to_name,
    assignedToPhotoURL: supabaseTask.assigned_to_photo_url,
    columnId: supabaseTask.column_id || 'column-1',
    groupId: supabaseTask.group_id,

    // Enhanced fields with defaults
    parentId: supabaseTask.parent_id,
    subjectId: supabaseTask.subject_id,
    examId: supabaseTask.exam_id,
    tags: Array.isArray(supabaseTask.tags) ? supabaseTask.tags : [],
    chapterTags: Array.isArray(supabaseTask.chapter_tags) ? supabaseTask.chapter_tags : [],
    difficultyLevel,
    timeEstimate: supabaseTask.time_estimate,
    actualTimeSpent: supabaseTask.actual_time_spent,
    completionPercentage: typeof supabaseTask.completion_percentage === 'number' ? supabaseTask.completion_percentage : 0,
    notes: supabaseTask.notes,
    viewCount: typeof supabaseTask.view_count === 'number' ? supabaseTask.view_count : 0,
    lastViewed: supabaseTask.last_viewed ? new Date(supabaseTask.last_viewed).getTime() : undefined,
  };
}

export default taskMigration;
